import { useState, useMemo, useRef, useEffect } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
  createColumnHelper,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import { ChevronLeft, ChevronRight, MoreHorizontal, CheckCircle, Clock, AlertTriangle, ChevronUp, ChevronDown, Search, ChevronDown as ChevronDownIcon, Settings2 as Filter } from 'lucide-react';
import type { User } from '@/types';

// Extend the ColumnMeta type to include sticky property
declare module '@tanstack/react-table' {
  // @ts-expect error
  interface ColumnMeta<TData, TValue> {
    sticky?: boolean;
  }
}

interface FilterConfig {
  column: string;
  label: string;
  type: 'select' | 'text' | 'range';
  options?: string[] | { label: string; value: string }[];
}

interface UsersTableProps {
  data: User[];
  filterColumns?: FilterConfig[];
  onRowClick?: (user: User) => void; // Add this prop for row click handler
  stickyColumns?: string[]; // Array of column IDs to make sticky
}

const UsersTable = ({ data, filterColumns, onRowClick, stickyColumns = [] }: UsersTableProps) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [tempFilters, setTempFilters] = useState<Record<string, string>>({});
  const filterRef = useRef<HTMLDivElement>(null);

  const columnHelper = createColumnHelper<User>();

  // Get unique values for filter dropdowns
  const uniqueStatuses = useMemo(() => {
    const statuses = [...new Set(data.map(item => item.status))];
    return statuses;
  }, [data]);

  const uniqueBusinesses = useMemo(() => {
    const businesses = [...new Set(data.map(item => item.business))];
    return businesses.sort();
  }, [data]);

  const capacityRanges = useMemo(() => {
    return [
      { label: '0-10 MW', min: 0, max: 10 },
      { label: '11-50 MW', min: 11, max: 50 },
      { label: '51-100 MW', min: 51, max: 100 },
      { label: '100+ MW', min: 101, max: Infinity },
    ];
  }, []);

  // Default filter configuration if none provided
  const defaultFilterColumns: FilterConfig[] = [
    {
      column: 'status',
      label: 'Status',
      type: 'select',
      options: uniqueStatuses
    },
    {
      column: 'business',
      label: 'Business',
      type: 'select',
      options: uniqueBusinesses
    },
    {
      column: 'capacity',
      label: 'Capacity',
      type: 'range',
      options: capacityRanges.map(range => ({ label: range.label, value: range.label }))
    }
  ];

  const activeFilterColumns = filterColumns || defaultFilterColumns;

  const columns = useMemo(() => [
    columnHelper.accessor('business', {
      header: 'Business',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex items-center">
            <div className={`w-10 h-10 rounded-full ${row.businessColor} flex items-center justify-center text-white font-semibold text-sm mr-3`}>
              {row.businessInitial}
            </div>
            <div className="text-sm font-medium text-gray-900">
              {info.getValue()}
            </div>
          </div>
        );
      },
      filterFn: 'includesString',
      meta: {
        sticky: stickyColumns.includes('business')
      }
    }),
    columnHelper.accessor('contact', {
      header: 'Contact',
      cell: (info) => {
        const contact = info.getValue();
        return (
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-sm mr-3">
              {contact.avatar}
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">
                {contact.name}
              </div>
              <div className="text-sm text-gray-500">
                {contact.email}
              </div>
            </div>
          </div>
        );
      },
      enableSorting: false,
      filterFn: (row, columnId, value) => {
        const contact = row.getValue(columnId) as { name: string; email: string };
        return contact.name.toLowerCase().includes(value.toLowerCase()) ||
               contact.email.toLowerCase().includes(value.toLowerCase());
      },
      meta: {
        sticky: stickyColumns.includes('contact')
      }
    }),
    columnHelper.accessor('project', {
      header: 'Projects',
      cell: (info) => (
        <div className="text-sm font-medium text-gray-900">
          {info.getValue()}
        </div>
      ),
      filterFn: 'includesString',
      meta: {
        sticky: stickyColumns.includes('project')
      }
    }),
    columnHelper.accessor('capacity', {
      header: 'Capacity (MW)',
      cell: (info) => (
        <div className="text-sm font-medium text-gray-900">
          {info.getValue()} MW
        </div>
      ),
      filterFn: (row, columnId, value) => {
        const capacity = row.getValue(columnId) as number;
        if (!value || value === 'all') return true;
        
        const range = capacityRanges.find(r => r.label === value);
        if (!range) return true;
        
        return capacity >= range.min && capacity <= range.max;
      },
      meta: {
        sticky: stickyColumns.includes('capacity')
      }
    }),
    columnHelper.accessor('status', {
      header: 'Status',
      cell: (info) => {
        const status = info.getValue();
        const row = info.row.original;
        const getStatusIcon = (status: string) => {
          switch (status) {
            case 'Verified':
              return <CheckCircle className="w-4 h-4 text-[#419E6A]" />;
            case 'Pending':
              return <Clock className="w-4 h-4 text-[#F1F5F9]" />;
            case 'Flagged':
              return <AlertTriangle className="w-4 h-4 text-red-600" />;
            default:
              return null;
          }
        };
        
        return (
          <div className="flex items-center">
            {getStatusIcon(status)}
            <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full text-white ${row.statusColor}`}>
              {status}
            </span>
          </div>
        );
      },
      filterFn: 'equalsString',
      meta: {
        sticky: stickyColumns.includes('status')
      }
    }),
    columnHelper.display({
      id: 'actions',
      header: 'Actions',
      cell: () => (
        <button 
          className="text-gray-400 hover:text-gray-600 transition-colors duration-150"
          onClick={(e) => {
            e.stopPropagation(); // Prevent row click when clicking actions button
          }}
        >
          <MoreHorizontal className="w-5 h-5" />
        </button>
      ),
      meta: {
        sticky: stickyColumns.includes('actions')
      }
    }),
  ], [columnHelper, capacityRanges, stickyColumns]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      globalFilter,
      columnFilters,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: 5,
      },
    },
  });

  // Close filter dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsFilterOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Helper functions for filters
  const getFilterValue = (columnId: string) => {
    return table.getColumn(columnId)?.getFilterValue() as string;
  };

  const setFilterValue = (columnId: string, value: string) => {
    table.getColumn(columnId)?.setFilterValue(value === 'all' ? undefined : value);
  };

  const applyFilters = () => {
    Object.entries(tempFilters).forEach(([columnId, value]) => {
      setFilterValue(columnId, value);
    });
    setIsFilterOpen(false);
  };

  const resetFilters = () => {
    setTempFilters({});
    setColumnFilters([]);
    setIsFilterOpen(false);
  };

  const updateTempFilter = (columnId: string, value: string) => {
    setTempFilters(prev => ({
      ...prev,
      [columnId]: value
    }));
  };


  // Helper function to get column widths (you can customize these)
  const getColumnWidth = (columnId: string): number => {
    const widthMap: Record<string, number> = {
      business: 200,
      contact: 250,
      project: 150,
      capacity: 150,
      status: 120,
      actions: 80
    };
    return widthMap[columnId] || 150; // Default to 150 if not found
  };

  const getStickyPosition = (columnId: string, isLeft: boolean = true) => {
    if (!stickyColumns.includes(columnId)) return {};
    
    const columnIndex = columns.findIndex(col => col.id === columnId || ('accessorKey' in col && (col as { accessorKey: string }).accessorKey === columnId));
    let position = 0;
    
    // Calculate position based on previous sticky columns
    for (let i = 0; i < columnIndex; i++) {
      const prevColumn = columns[i];
      const prevColumnId = prevColumn.id || ('accessorKey' in prevColumn && (prevColumn as { accessorKey: string }).accessorKey);
      if (prevColumnId && stickyColumns.includes(prevColumnId)) {
        position += getColumnWidth(prevColumnId);
      }
    }
    
    return {
      position: isLeft ? 'sticky' as const : undefined,
      left: isLeft ? `${position}px` : undefined,
      zIndex: 10,
      backgroundColor: 'white'
    };
  };

  const handleRowClick = (user: User) => {
    if (onRowClick) {
      onRowClick(user);
    } else {
      // Default behavior - you can customize this
      console.log('Row clicked:', user);
      // For example, you could navigate to a detail page:
      // navigate(`/users/${user.id}`);
    }
  };

  // Render filter input based on type
  const renderFilterInput = (filterConfig: FilterConfig) => {
    const { column, label, type, options } = filterConfig;
    const value = tempFilters[column] || getFilterValue(column) || 'all';

    if (type === 'text') {
      return (
        <div className="mb-4" key={column}>
          <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
          <input
            type="text"
            value={value === 'all' ? '' : value}
            onChange={(e) => updateTempFilter(column, e.target.value || 'all')}
            placeholder={`Filter ${label.toLowerCase()}...`}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
      );
    }

    // Select and range types use dropdown
    const selectOptions = Array.isArray(options) 
      ? options.map(opt => typeof opt === 'string' ? { label: opt, value: opt } : opt)
      : [];

    return (
      <div className="mb-4" key={column}>
        <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        <div className="relative">
          <select
            value={value}
            onChange={(e) => updateTempFilter(column, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 appearance-none bg-white"
          >
            <option value="all">Select</option>
            {selectOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <ChevronDownIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
        </div>
      </div>
    );
  };

  const hasActiveFilters = columnFilters.length > 0;

  return (
    <div className="w-full bg-white">
      {/* Search and Filter Controls */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Filter Button */}
          <div className="relative" ref={filterRef}>
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className={`flex items-center space-x-2 px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
                hasActiveFilters 
                  ? 'border-purple-300 bg-purple-50 text-purple-700' 
                  : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <Filter className="w-4 h-4" />
              <span>Filter</span>
              {hasActiveFilters && (
                <span className="bg-purple-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {columnFilters.length}
                </span>
              )}
            </button>

            {/* Filter Dropdown */}
            {isFilterOpen && (
              <div className="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
                  
                  {/* Dynamic Filter Inputs */}
                  {activeFilterColumns.map(renderFilterInput)}

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <button
                      onClick={resetFilters}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Reset
                    </button>
                    <button
                      onClick={applyFilters}
                      className="flex-1 px-4 py-2 bg-orange-500 text-white rounded-lg text-sm font-medium hover:bg-orange-600 transition-colors"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              value={globalFilter ?? ''}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 w-64"
              placeholder="Search"
            />
          </div>

          {/* <div className="text-sm text-gray-700">
            Showing {table.getRowModel().rows.length} of {data.length} entries
          </div> */}
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show:</span>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => {
              table.setPageSize(Number(e.target.value));
            }}
            className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {[5, 10, 20, 50].map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
          <span className="text-sm text-gray-700">entries</span>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    const isSticky = header.column.columnDef.meta?.sticky;
                    const columnId = header.column.id;
                    const stickyStyles = isSticky ? getStickyPosition(columnId) : {};
                    
                    return (
                      <th
                        key={header.id}
                        className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none border-r border-gray-200 last:border-r-0"
                        onClick={header.column.getToggleSortingHandler()}
                        style={stickyStyles}
                      >
                        <div className="flex items-center space-x-1">
                          <span>
                            {header.isPlaceholder
                              ? null
                              : flexRender(header.column.columnDef.header, header.getContext())}
                          </span>
                          {header.column.getCanSort() && (
                            <div className="flex flex-col">
                              {header.column.getIsSorted() === 'asc' ? (
                                <ChevronUp className="w-3 h-3 text-gray-600" />
                              ) : header.column.getIsSorted() === 'desc' ? (
                                <ChevronDown className="w-3 h-3 text-gray-600" />
                              ) : (
                                <div className="w-3 h-3 opacity-40">
                                  <ChevronUp className="w-3 h-3 -mb-1" />
                                  <ChevronDown className="w-3 h-3" />
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {table.getRowModel().rows.map((row) => (
                <tr 
                  key={row.id} 
                  onClick={() => handleRowClick(row.original)}
                  className="hover:bg-gray-50 cursor-pointer transition-colors duration-150"
                >
                  {row.getVisibleCells().map((cell) => {
                    const isSticky = cell.column.columnDef.meta?.sticky;
                    const columnId = cell.column.id;
                    const stickyStyles = isSticky ? getStickyPosition(columnId) : {};
                    return (
                      <td 
                        key={cell.id} 
                        className="px-6 py-4 whitespace-nowrap"
                        style={stickyStyles}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-start space-x-6 py-4">
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="text-gray-500 disabled:opacity-50"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>

          {Array.from({ length: Math.min(5, table.getPageCount()) }, (_, i) => {
            const pageNumber = i + 1;
            const currentPage = table.getState().pagination.pageIndex + 1;
            return (
              <button
                key={pageNumber}
                onClick={() => table.setPageIndex(pageNumber - 1)}
                className={`h-8 w-8 flex items-center justify-center rounded-full text-sm font-medium ${
                  currentPage === pageNumber
                    ? 'bg-amber-100 text-black'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {pageNumber}
              </button>
            );
          })}

          {table.getPageCount() > 5 && <span className="text-gray-500">...</span>}

          {table.getPageCount() > 5 && (
            <button
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              className="h-8 w-8 flex items-center justify-center rounded-full text-sm font-medium text-gray-700 hover:bg-gray-100"
            >
              {table.getPageCount()}
            </button>
          )}

          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="text-gray-500 disabled:opacity-50"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default UsersTable;
